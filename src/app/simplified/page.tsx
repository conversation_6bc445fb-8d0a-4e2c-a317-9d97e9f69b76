"use client";

import React, { useState } from 'react';
import { SmartLanding } from '@/components/simplified-ux/smart-landing';
import { InstantPractice } from '@/components/simplified-ux/instant-practice';
import { SmartContract } from '@/components/simplified-ux/smart-contract';
import { GuidedTutorial } from '@/components/simplified-ux/guided-tutorial';

type AppMode = 'landing' | 'practice' | 'contract' | 'learn';

export default function SimplifiedApp() {
  const [mode, setMode] = useState<AppMode>('landing');

  const handleModeChange = (newMode: AppMode) => {
    setMode(newMode);
    
    // Update URL without page reload for better UX
    const url = new URL(window.location.href);
    if (newMode === 'landing') {
      url.searchParams.delete('mode');
    } else {
      url.searchParams.set('mode', newMode);
    }
    window.history.replaceState({}, '', url.toString());
  };

  const handleBackToLanding = () => {
    setMode('landing');
  };

  // Check URL parameters on mount to restore state
  React.useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const urlMode = urlParams.get('mode') as AppMode;
    if (urlMode && ['practice', 'contract', 'learn'].includes(urlMode)) {
      setMode(urlMode);
    }
  }, []);

  return (
    <div className="min-h-screen">
      {mode === 'landing' && (
        <SmartLanding onModeSelect={handleModeChange} />
      )}
      
      {mode === 'practice' && (
        <InstantPractice onBack={handleBackToLanding} />
      )}
      
      {mode === 'contract' && (
        <SmartContract onBack={handleBackToLanding} />
      )}
      
      {mode === 'learn' && (
        <GuidedTutorial onBack={handleBackToLanding} />
      )}
    </div>
  );
}
