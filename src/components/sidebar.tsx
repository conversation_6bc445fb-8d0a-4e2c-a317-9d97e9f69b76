"use client";

import type React from "react";

import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import {
	FileText,
	MessageSquare,
	BarChart,
	Search,
	GitCompare,
	Settings,
	Menu,
	FolderTree,
	ChevronDown,
	ChevronRight,
	CreditCard,
	LineChart,
	Users,
	Shield,
	BookOpen,
	Sparkles,
	Upload,
	Database,
	Target,
	Gavel,
	User,
	MessageCircle,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { useState } from "react";
import { ThemeToggle } from "@/app/theme-toggle";

interface RouteCategory {
	title: string;
	icon: React.ElementType;
	routes: RouteItem[];
	defaultOpen?: boolean;
}

export function Sidebar({ className }: React.HTMLAttributes<HTMLDivElement>) {
	const pathname = usePathname();
	const [open, setOpen] = useState(false);
	const [expandedCategories, setExpandedCategories] = useState<
		Record<string, boolean>
	>({
		documents: true,
		"ai & automation": true,
		"analysis & comparison": true,
		"legal tools": true,
		collaboration: true,
		"account & settings": false,
	});

	const toggleCategory = (category: string) => {
		setExpandedCategories((prev) => ({
			...prev,
			[category]: !prev[category],
		}));
	};

	// Standalone routes
	const standaloneRoutes = [
		{
			label: "Chat",
			icon: MessageSquare,
			href: "/chat",
			active: pathname === "/chat",
		},
	];

	// Categorized routes
	const routeCategories: RouteCategory[] = [
		{
			title: "Documents",
			icon: FileText,
			defaultOpen: true,
			routes: [
				{
					label: "All Documents",
					href: "/documents",
					icon: FileText,
					active: pathname === "/documents",
				},
				{
					label: "Upload Document",
					href: "/documents/upload",
					icon: Upload,
					active: pathname === "/documents/upload",
				},
				{
					label: "Document Organization",
					href: "/document-organization",
					icon: FolderTree,
					active: pathname === "/document-organization",
				},
			],
		},
		{
			title: "AI & Automation",
			icon: Sparkles,
			defaultOpen: true,
			routes: [
				{
					label: "Document Automation",
					href: "/document-automation",
					icon: Sparkles,
					active: pathname === "/document-automation",
				},
				{
					label: "Negotiation Simulator",
					href: "/negotiation-simulator",
					icon: Target,
					active:
						pathname === "/negotiation-simulator" ||
						pathname.startsWith("/negotiation-simulator/"),
				},
				{
					label: "Compliance Auditor",
					href: "/compliance-auditor",
					icon: Shield,
					active:
						pathname === "/compliance-auditor" ||
						pathname.startsWith("/compliance-auditor/"),
				},
			],
		},
		{
			title: "Analysis & Comparison",
			icon: LineChart,
			defaultOpen: true,
			routes: [
				{
					label: "Document Comparison",
					href: "/document-comparison",
					icon: GitCompare,
					active:
						pathname === "/document-comparison" ||
						pathname.startsWith("/document-comparison/"),
				},
				{
					label: "Compare Sections",
					href: "/compare/sections",
					icon: GitCompare,
					active: pathname === "/compare/sections",
				},
				{
					label: "Basic Comparison",
					href: "/compare/basic",
					icon: GitCompare,
					active: pathname === "/compare/basic",
				},
				{
					label: "Enhanced Comparison",
					href: "/compare/enhanced",
					icon: GitCompare,
					active: pathname === "/compare/enhanced",
				},
				{
					label: "Analyze Multiple",
					href: "/analyze/multiple",
					icon: BarChart,
					active: pathname === "/analyze/multiple",
				},
				{
					label: "Analytics Dashboard",
					href: "/analytics",
					icon: BarChart,
					active: pathname === "/analytics",
				},
			],
		},
		{
			title: "Legal Tools",
			icon: Gavel,
			defaultOpen: true,
			routes: [
				{
					label: "Contract Playbooks",
					href: "/contract-playbooks",
					icon: BookOpen,
					active:
						pathname === "/contract-playbooks" ||
						pathname.startsWith("/contract-playbooks/"),
				},
				{
					label: "Negotiation Playbooks",
					href: "/negotiation-playbooks",
					icon: Target,
					active:
						pathname === "/negotiation-playbooks" ||
						pathname.startsWith("/negotiation-playbooks/"),
				},
				{
					label: "Analysis Results",
					href: "/contract-playbooks/analyses",
					icon: BarChart,
					active:
						pathname === "/contract-playbooks/analyses" ||
						pathname.startsWith("/contract-playbooks/analyses/"),
				},
				{
					label: "Depositions",
					href: "/depositions",
					icon: Users,
					active:
						pathname === "/depositions" || pathname.startsWith("/depositions/"),
				},
				{
					label: "Case Search",
					href: "/case-search",
					icon: Search,
					active: pathname === "/case-search",
				},
				{
					label: "Precedent Analysis",
					href: "/precedent-analysis",
					icon: Database,
					active: pathname === "/precedent-analysis",
				},
				{
					label: "Clause Library",
					href: "/clause-library",
					icon: Database,
					active: pathname === "/clause-library",
				},
			],
		},
		{
			title: "Collaboration",
			icon: Users,
			defaultOpen: true,
			routes: [
				{
					label: "Collaboration Suite",
					href: "/collaboration",
					icon: Users,
					active: pathname === "/collaboration",
				},
				{
					label: "Real-time Editing",
					href: "/collaboration?tab=editor",
					icon: FileText,
					active: pathname === "/collaboration" && typeof window !== 'undefined' && window.location.search.includes('tab=editor'),
				},
				{
					label: "Workflows",
					href: "/collaboration?tab=workflows",
					icon: GitCompare,
					active: pathname === "/collaboration" && typeof window !== 'undefined' && window.location.search.includes('tab=workflows'),
				},
				{
					label: "Task Management",
					href: "/collaboration?tab=tasks",
					icon: Target,
					active: pathname === "/collaboration" && typeof window !== 'undefined' && window.location.search.includes('tab=tasks'),
				},
			],
		},
		{
			title: "Account & Settings",
			icon: Settings,
			defaultOpen: false,
			routes: [
				{
					label: "Profile",
					href: "/profile",
					icon: User,
					active: pathname === "/profile",
				},
				{
					label: "Subscription",
					href: "/subscription",
					icon: CreditCard,
					active:
						pathname === "/subscription" ||
						pathname.startsWith("/subscription/"),
				},
				{
					label: "Feedback",
					href: "/feedback",
					icon: MessageCircle,
					active: pathname === "/feedback",
				},
			],
		},
	];

	return (
		<>
			<Sheet open={open} onOpenChange={setOpen}>
				<SheetTrigger asChild>
					<Button
						variant="outline"
						size="icon"
						className="md:hidden fixed left-4 top-4 z-40"
					>
						<Menu className="h-4 w-4" />
						<span className="sr-only">Toggle Menu</span>
					</Button>
				</SheetTrigger>
				<SheetContent side="left" className="p-0 bg-card dark:bg-[#1a1a1a]">
					<MobileSidebar
						standaloneRoutes={standaloneRoutes}
						routeCategories={routeCategories}
						expandedCategories={expandedCategories}
						toggleCategory={toggleCategory}
						setOpen={setOpen}
					/>
				</SheetContent>
			</Sheet>

			<aside
				className={cn(
					"hidden md:flex h-screen flex-col bg-card dark:bg-[#1a1a1a] border-r fixed left-0 top-0 z-30 w-64",
					className
				)}
			>
				<div className="p-2 flex items-center justify-between h-12">
					<div className="flex items-center gap-2">
						<Image
							src="/logos/trans-512.png"
							alt="Docgic Logo"
							width={300}
							height={300}
							className="w-32 bg-cover dark:block hidden"
						/>
						<Image
							src="/logos/light-512.png"
							alt="Docgic Logo"
							width={300}
							height={300}
							className="w-32 bg-cover dark:hidden block"
						/>
					</div>
					<div className="flex justify-end">
						<ThemeToggle />
					</div>
				</div>
				<ScrollArea className="flex-1 px-3">
					<div className="space-y-1 py-2">
						{/* Standalone Routes */}
						{standaloneRoutes.map((route) => (
							<Link
								key={route.href}
								href={route.href}
								className={cn(
									"flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors",
									route.active
										? "bg-secondary dark:bg-[#252525] text-foreground"
										: "text-muted-foreground hover:bg-secondary/50 dark:hover:bg-[#252525]/50 hover:text-foreground"
								)}
							>
								<route.icon className="h-4 w-4 mr-3" />
								{route.label}
							</Link>
						))}

						<div className="my-2 border-t border-border/40"></div>

						{/* Categorized Routes */}
						{routeCategories.map((category) => (
							<div key={category.title} className="mb-2">
								<button
									onClick={() => toggleCategory(category.title.toLowerCase())}
									className={cn(
										"w-full flex items-center justify-between px-3 py-2 rounded-lg text-sm font-medium transition-colors",
										"text-muted-foreground hover:bg-secondary/50 dark:hover:bg-[#252525]/50 hover:text-foreground"
									)}
								>
									<div className="flex items-center">
										<category.icon className="h-4 w-4 mr-3" />
										{category.title}
									</div>
									{expandedCategories[category.title.toLowerCase()] ? (
										<ChevronDown className="h-4 w-4" />
									) : (
										<ChevronRight className="h-4 w-4" />
									)}
								</button>

								{expandedCategories[category.title.toLowerCase()] && (
									<div className="ml-4 pl-2 border-l border-border/40 mt-1 space-y-1">
										{category.routes.map((route) => (
											<Link
												key={route.href}
												href={route.href}
												className={cn(
													"flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors",
													route.active
														? "bg-secondary dark:bg-[#252525] text-foreground"
														: "text-muted-foreground hover:bg-secondary/50 dark:hover:bg-[#252525]/50 hover:text-foreground"
												)}
											>
												<route.icon className="h-4 w-4 mr-3" />
												{route.label}
											</Link>
										))}
									</div>
								)}
							</div>
						))}
					</div>
				</ScrollArea>
			</aside>
		</>
	);
}

interface BaseRouteItem {
	icon: React.ElementType;
	href: string;
	active?: boolean;
}

interface StandardRouteItem extends BaseRouteItem {
	label: string;
	active?: boolean;
	title?: never;
	variant?: never;
}

interface TitleRouteItem extends BaseRouteItem {
	title: string;
	variant: string;
	label?: never;
	active?: never;
}

type RouteItem = StandardRouteItem | TitleRouteItem;

interface MobileSidebarProps {
	standaloneRoutes: RouteItem[];
	routeCategories: RouteCategory[];
	expandedCategories: Record<string, boolean>;
	toggleCategory: (category: string) => void;
	setOpen: (open: boolean) => void;
}

function MobileSidebar({
	standaloneRoutes,
	routeCategories,
	expandedCategories,
	toggleCategory,
	setOpen,
}: MobileSidebarProps) {
	return (
		<div className="flex flex-col h-full bg-card dark:bg-[#1a1a1a]">
			<div className="p-2 flex items-center justify-between h-12 ">
				<div className="flex items-center gap-2">
					<Image
						src="/logos/trans-512.png"
						alt="Docgic Logo"
						width={300}
						height={300}
						className="w-32 bg-cover dark:block hidden"
					/>
					<Image
						src="/logos/light-512.png"
						alt="Docgic Logo"
						width={300}
						height={300}
						className="w-32 bg-cover dark:hidden"
					/>
				</div>
				<div className="p-3 border-[#333] flex justify-end mr-6">
					<ThemeToggle />
				</div>
			</div>

			<ScrollArea className="flex-1 p-3">
				<div className="space-y-1 py-2">
					{/* Standalone Routes */}
					{standaloneRoutes.map((route) => (
						<Link
							key={route.href}
							href={route.href}
							onClick={() => setOpen(false)}
							className={cn(
								"flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors",
								route.active
									? "bg-secondary dark:bg-[#252525] text-foreground"
									: "text-muted-foreground hover:bg-secondary/50 dark:hover:bg-[#252525]/50 hover:text-foreground"
							)}
						>
							<route.icon className="h-4 w-4 mr-3" />
							{route.label}
						</Link>
					))}

					<div className="my-2 border-t border-border/40"></div>

					{/* Categorized Routes */}
					{routeCategories.map((category) => (
						<div key={category.title} className="mb-2">
							<button
								onClick={() => toggleCategory(category.title.toLowerCase())}
								className={cn(
									"w-full flex items-center justify-between px-3 py-2 rounded-lg text-sm font-medium transition-colors",
									"text-muted-foreground hover:bg-secondary/50 dark:hover:bg-[#252525]/50 hover:text-foreground"
								)}
							>
								<div className="flex items-center">
									<category.icon className="h-4 w-4 mr-3" />
									{category.title}
								</div>
								{expandedCategories[category.title.toLowerCase()] ? (
									<ChevronDown className="h-4 w-4" />
								) : (
									<ChevronRight className="h-4 w-4" />
								)}
							</button>

							{expandedCategories[category.title.toLowerCase()] && (
								<div className="ml-4 pl-2 border-l border-border/40 mt-1 space-y-1">
									{category.routes.map((route) => (
										<Link
											key={route.href}
											href={route.href}
											onClick={() => setOpen(false)}
											className={cn(
												"flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors",
												route.active
													? "bg-secondary dark:bg-[#252525] text-foreground"
													: "text-muted-foreground hover:bg-secondary/50 dark:hover:bg-[#252525]/50 hover:text-foreground"
											)}
										>
											<route.icon className="h-4 w-4 mr-3" />
											{route.label}
										</Link>
									))}
								</div>
							)}
						</div>
					))}
				</div>
			</ScrollArea>
		</div>
	);
}
