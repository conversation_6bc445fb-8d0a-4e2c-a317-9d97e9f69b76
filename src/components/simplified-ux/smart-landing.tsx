"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { 
  MessageSquare, 
  FileText, 
  GraduationCap,
  ArrowRight,
  Zap,
  Clock,
  Users
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useRouter } from 'next/navigation';

interface IntentCardProps {
  title: string;
  description: string;
  action: string;
  icon: React.ComponentType<{ className?: string }>;
  time: string;
  difficulty: string;
  onClick: () => void;
  featured?: boolean;
}

function IntentCard({ title, description, action, icon: Icon, time, difficulty, onClick, featured }: IntentCardProps) {
  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      className="w-full"
    >
      <Card 
        className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
          featured ? 'ring-2 ring-primary/20 bg-gradient-to-br from-primary/5 to-primary/10' : ''
        }`}
        onClick={onClick}
      >
        <CardContent className="p-6">
          <div className="flex items-start gap-4">
            <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
              featured ? 'bg-primary text-primary-foreground' : 'bg-muted'
            }`}>
              <Icon className="h-6 w-6" />
            </div>
            
            <div className="flex-1 space-y-3">
              <div>
                <h3 className="text-lg font-semibold">{title}</h3>
                <p className="text-muted-foreground text-sm">{description}</p>
              </div>
              
              <div className="flex items-center gap-3">
                <Badge variant="outline" className="text-xs">
                  <Clock className="h-3 w-3 mr-1" />
                  {time}
                </Badge>
                <Badge variant="outline" className="text-xs">
                  {difficulty}
                </Badge>
              </div>
              
              <Button 
                className="w-full gap-2" 
                variant={featured ? "default" : "outline"}
              >
                {action}
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

export function SmartLanding() {
  const router = useRouter();

  const handlePracticeNow = () => {
    router.push('/practice');
  };

  const handleUploadContract = () => {
    router.push('/contract');
  };

  const handleLearnBasics = () => {
    router.push('/learn');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center space-y-6 mb-12"
          >
            <div className="space-y-4">
              <h1 className="text-4xl md:text-5xl font-bold tracking-tight">
                What would you like to do today?
              </h1>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                Choose your path to better negotiation skills. Each option is designed to get you practicing in under 30 seconds.
              </p>
            </div>
            
            {/* Quick Stats */}
            <div className="flex items-center justify-center gap-8 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                <span>10,000+ negotiations practiced</span>
              </div>
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4" />
                <span>AI-powered responses</span>
              </div>
            </div>
          </motion.div>

          {/* Intent Cards */}
          <div className="grid gap-6 md:gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <IntentCard
                title="Practice Negotiating"
                description="Start a conversation with an AI negotiator right now. No setup required, just natural conversation."
                action="Start Practicing"
                icon={MessageSquare}
                time="2 min"
                difficulty="Any level"
                onClick={handlePracticeNow}
                featured={true}
              />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <IntentCard
                title="Review My Contract"
                description="Upload a contract and practice negotiating its specific terms with an AI that understands your document."
                action="Upload Contract"
                icon={FileText}
                time="5 min"
                difficulty="Intermediate"
                onClick={handleUploadContract}
              />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <IntentCard
                title="Learn the Basics"
                description="New to negotiation? Take a quick interactive tutorial that builds your confidence step by step."
                action="Start Tutorial"
                icon={GraduationCap}
                time="5 min"
                difficulty="Beginner"
                onClick={handleLearnBasics}
              />
            </motion.div>
          </div>

          {/* Trust Indicators */}
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="mt-16 text-center"
          >
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-muted/50 rounded-full text-sm text-muted-foreground">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              <span>All systems operational • Instant AI responses</span>
            </div>
          </motion.div>

          {/* Quick Help */}
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
            className="mt-8 text-center"
          >
            <p className="text-sm text-muted-foreground">
              Not sure which option to choose?{' '}
              <button 
                onClick={handlePracticeNow}
                className="text-primary hover:underline font-medium"
              >
                Start with Practice Negotiating
              </button>
              {' '}— it's the most popular choice.
            </p>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
