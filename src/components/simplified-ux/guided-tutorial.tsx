"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  <PERSON>Left, 
  ArrowRight,
  MessageSquare,
  TrendingUp,
  Target,
  CheckCircle,
  Play,
  Lightbulb
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useRouter } from 'next/navigation';

interface GuidedTutorialProps {
  onBack?: () => void;
}

const tutorialSteps = [
  {
    id: 1,
    title: "Welcome to Negotiation Practice",
    content: "Learn the basics of effective negotiation through interactive conversation with AI partners.",
    action: "Let's start!",
    visual: MessageSquare,
    color: "bg-blue-100 text-blue-600"
  },
  {
    id: 2,
    title: "Natural Conversation",
    content: "Type naturally like you're talking to a colleague. The AI understands context and responds realistically.",
    action: "Try it now",
    visual: MessageSquare,
    color: "bg-green-100 text-green-600",
    interactive: true,
    demoMessage: "I'm interested in your software for my team of 20 people"
  },
  {
    id: 3,
    title: "Relationship Tracking",
    content: "Watch how your approach affects trust, respect, and pressure levels in real-time.",
    action: "See metrics",
    visual: TrendingUp,
    color: "bg-purple-100 text-purple-600"
  },
  {
    id: 4,
    title: "Strategic Guidance",
    content: "Get smart suggestions and tips to improve your negotiation approach.",
    action: "Get suggestions",
    visual: Target,
    color: "bg-orange-100 text-orange-600"
  },
  {
    id: 5,
    title: "You're Ready!",
    content: "You now understand the basics. Ready to start your first real negotiation practice?",
    action: "Start practicing",
    visual: CheckCircle,
    color: "bg-green-100 text-green-600"
  }
];

export function GuidedTutorial({ onBack }: GuidedTutorialProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [isCompleted, setIsCompleted] = useState(false);
  const router = useRouter();

  const handleNext = () => {
    if (currentStep < tutorialSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      setIsCompleted(true);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      router.push('/simplified');
    }
  };

  const handleStartPracticing = () => {
    if (onBack) {
      onBack();
      // Signal to parent to switch to practice mode
      setTimeout(() => {
        const event = new CustomEvent('startPractice');
        window.dispatchEvent(event);
      }, 100);
    } else {
      router.push('/practice');
    }
  };

  const currentStepData = tutorialSteps[currentStep];
  const progress = ((currentStep + 1) / tutorialSteps.length) * 100;

  if (isCompleted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto text-center space-y-8">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              className="space-y-6"
            >
              <div className="w-20 h-20 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto">
                <CheckCircle className="h-10 w-10 text-green-600" />
              </div>
              
              <div>
                <h1 className="text-3xl font-bold mb-4">Tutorial Complete!</h1>
                <p className="text-lg text-muted-foreground">
                  You're now ready to start practicing real negotiations with AI partners.
                </p>
              </div>
              
              <div className="flex gap-4 justify-center">
                <Button variant="outline" onClick={handleBack}>
                  Back to Home
                </Button>
                <Button onClick={handleStartPracticing} className="gap-2">
                  <Play className="h-4 w-4" />
                  Start Practicing
                </Button>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-4">
              <Button variant="ghost" onClick={handleBack} className="gap-2">
                <ArrowLeft className="h-4 w-4" />
                Back
              </Button>
              <div>
                <h1 className="text-2xl font-bold">Learn the Basics</h1>
                <p className="text-muted-foreground">Interactive 5-minute tutorial</p>
              </div>
            </div>
            
            <Badge variant="outline">
              Step {currentStep + 1} of {tutorialSteps.length}
            </Badge>
          </div>

          {/* Progress */}
          <div className="mb-8">
            <div className="flex justify-between text-sm mb-2">
              <span>Progress</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>

          {/* Tutorial Step */}
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="border-2 border-primary/20">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center ${currentStepData.color}`}>
                      {React.createElement(currentStepData.visual, { className: "h-6 w-6" })}
                    </div>
                    {currentStepData.title}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <p className="text-lg text-muted-foreground">
                    {currentStepData.content}
                  </p>

                  {/* Interactive Demo for Step 2 */}
                  {currentStepData.interactive && (
                    <div className="space-y-4">
                      <div className="p-4 bg-muted/50 rounded-lg">
                        <h4 className="font-medium mb-2 flex items-center gap-2">
                          <Lightbulb className="h-4 w-4" />
                          Try This Example
                        </h4>
                        <div className="bg-background p-3 rounded border">
                          <div className="text-sm text-muted-foreground mb-1">You:</div>
                          <div className="font-medium">{currentStepData.demoMessage}</div>
                        </div>
                        <div className="mt-3 bg-blue-50 dark:bg-blue-950/20 p-3 rounded border">
                          <div className="text-sm text-muted-foreground mb-1">AI Response:</div>
                          <div className="font-medium text-blue-900 dark:text-blue-100">
                            "That's a great team size! For 20 users, I'd recommend our Professional plan. 
                            What's most important to you - features, support level, or pricing flexibility?"
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Metrics Demo for Step 3 */}
                  {currentStep === 2 && (
                    <div className="grid grid-cols-3 gap-4">
                      <div className="text-center p-3 bg-green-50 dark:bg-green-950/20 rounded-lg">
                        <div className="text-lg font-bold text-green-600">75%</div>
                        <div className="text-xs text-muted-foreground">Trust</div>
                      </div>
                      <div className="text-center p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                        <div className="text-lg font-bold text-blue-600">68%</div>
                        <div className="text-xs text-muted-foreground">Respect</div>
                      </div>
                      <div className="text-center p-3 bg-yellow-50 dark:bg-yellow-950/20 rounded-lg">
                        <div className="text-lg font-bold text-yellow-600">25%</div>
                        <div className="text-xs text-muted-foreground">Pressure</div>
                      </div>
                    </div>
                  )}

                  {/* Suggestions Demo for Step 4 */}
                  {currentStep === 3 && (
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Smart Suggestions:</h4>
                      <div className="space-y-2">
                        {[
                          "Ask about their biggest challenges",
                          "Explore volume discounts",
                          "Discuss implementation timeline"
                        ].map((suggestion, index) => (
                          <div key={index} className="p-2 bg-muted/50 rounded text-sm border-l-2 border-primary/50">
                            {suggestion}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Navigation */}
                  <div className="flex items-center justify-between pt-4">
                    <Button 
                      variant="outline" 
                      onClick={handlePrevious}
                      disabled={currentStep === 0}
                    >
                      Previous
                    </Button>
                    
                    <div className="flex gap-2">
                      {tutorialSteps.map((_, index) => (
                        <div
                          key={index}
                          className={`w-2 h-2 rounded-full transition-colors ${
                            index <= currentStep ? 'bg-primary' : 'bg-muted'
                          }`}
                        />
                      ))}
                    </div>

                    <Button onClick={handleNext} className="gap-2">
                      {currentStep === tutorialSteps.length - 1 ? 'Complete' : currentStepData.action}
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
}
